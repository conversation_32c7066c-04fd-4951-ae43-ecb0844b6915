<template>
  <component
    is="a-modal"
    :width="tool.getDevice() === 'mobile' ? '100%' : '600px'"
    v-model:visible="visible"
    :title="title"
    :mask-closable="false"
    :ok-loading="loading"
    @cancel="close"
    @before-ok="submit">
    <!-- 表单信息 start -->
    <a-form ref="formRef" :model="formData" :rules="rules" :auto-label-width="true">
      <a-form-item label="主播昵称" field="anchor_name">
        <a-input v-model="formData.anchor_name" placeholder="请输入主播昵称" />
      </a-form-item>
      <a-form-item label="主播头像" field="avatar">
        <sa-upload-image v-model="formData.avatar" :limit="1" :multiple="false" />
      </a-form-item>
      <a-form-item label="直播间标题" field="title">
        <a-input v-model="formData.title" placeholder="请输入直播间标题" />
      </a-form-item>

      <a-form-item label="直播间简介" field="intro">
        <a-textarea v-model="formData.intro" placeholder="请输入直播间简介" />
      </a-form-item>

      <a-form-item label="直播间封面" field="cover_url">
        <sa-upload-image v-model="formData.cover_url" :limit="1" :multiple="false" />
      </a-form-item>

      <a-form-item label="直播间公告" field="announcement">
        <a-textarea v-model="formData.announcement" placeholder="请输入直播间公告" />
      </a-form-item>

      <a-form-item label="粉丝数" field="follow_num">
        <a-input-number v-model="formData.follow_num" placeholder="请输入粉丝数" />
      </a-form-item>

      <a-form-item label="基础人气值" field="popularity">
        <a-input-number v-model="formData.popularity" placeholder="请输入基础人气值" />
      </a-form-item>

      <a-form-item label="累计观看人数" field="total_views">
        <a-input-number v-model="formData.total_views" placeholder="请输入累计观看人数" />
      </a-form-item>
      <a-form-item label="总点赞数" field="total_likes">
        <a-input-number v-model="formData.total_likes" placeholder="请输入总点赞数" />
      </a-form-item>
      <a-form-item label="总评论数" field="total_comments">
        <a-input-number v-model="formData.total_comments" placeholder="请输入总评论数" />
      </a-form-item>
      <a-form-item label="总分享数" field="total_shares">
        <a-input-number v-model="formData.total_shares" placeholder="请输入总分享数" />
      </a-form-item>
    </a-form>
    <!-- 表单信息 end -->
  </component>
</template>

<script setup>
import { ref, reactive, computed } from 'vue'
import tool from '@/utils/tool'
import { Message, Modal } from '@arco-design/web-vue'
import api from '../api/rooms'

const emit = defineEmits(['success'])
// 引用定义
const visible = ref(false)
const loading = ref(false)
const formRef = ref()
const mode = ref('')

let title = computed(() => {
  return '直播间列表' + (mode.value == 'add' ? '-新增' : '-编辑')
})

// 表单初始值
const initialFormData = {
  title: '',
  announcement: '',
  anchor_name: '',
  avatar: '',
  room_id: null,
  intro: '',
  popularity: '0',
  follow_num: '0',
  cover_url: '',
  total_views: '0',
  total_likes: '0',
  total_comments: '0',
  total_shares: '0',
}

// 表单信息
const formData = reactive({ ...initialFormData })

// 验证规则
const rules = {
  title: [{ required: true, message: '直播间标题必需填写' }],
  anchor_name: [{ required: true, message: '主播昵称必需填写' }],
  avatar: [{ required: true, message: '主播头像必需填写' }],
}

// 打开弹框
const open = async (type = 'add') => {
  mode.value = type
  // 重置表单数据
  Object.assign(formData, initialFormData)
  formRef.value.clearValidate()
  visible.value = true
  await initPage()
}

// 初始化页面数据
const initPage = async () => {}

// 设置数据
const setFormData = async (data) => {
  for (const key in formData) {
    if (data[key] != null && data[key] != undefined) {
      formData[key] = data[key]
    }
  }
}

// 数据保存
const submit = async (done) => {
  const validate = await formRef.value?.validate()
  if (!validate) {
    loading.value = true
    let data = { ...formData }
    let result = {}
    if (mode.value === 'add') {
      // 添加数据
      data.room_id = undefined
      result = await api.save(data)
    } else {
      // 修改数据
      result = await api.update(data.room_id, data)
    }
    if (result.code === 200) {
      Message.success('操作成功')
      emit('success')
      done(true)
    }
    // 防止连续点击提交
    setTimeout(() => {
      loading.value = false
    }, 500)
  }
  done(false)
}

// 关闭弹窗
const close = () => (visible.value = false)

defineExpose({ open, setFormData })
</script>
