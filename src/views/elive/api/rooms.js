import {request} from '@/utils/request.js'

/**
 * 直播间列表 API接口
 */
export default {

    /**
     * 数据列表
     * @returns
     */
    getPageList(params = {}) {
        return request({
            url: '/elive/LiveRooms/index',
            method: 'get',
            params
        })
    },

    /**
     * 添加数据
     * @returns
     */
    save(params = {}) {
        return request({
            url: '/elive/LiveRooms/save',
            method: 'post',
            data: params
        })
    },

    /**
     * 更新数据
     * @returns
     */
    update(id, data = {}) {
        return request({
            url: '/elive/LiveRooms/update?id=' + id,
            method: 'put',
            data
        })
    },

    /**
     * 读取数据
     * @returns
     */
    read(id) {
        return request({
            url: '/elive/LiveRooms/read?id=' + id,
            method: 'get'
        })
    },

    /**
     * 删除数据
     * @returns
     */
    destroy(data) {
        return request({
            url: '/elive/LiveRooms/destroy',
            method: 'delete',
            data
        })
    },
    startLive(data) {
        return request({
            url: '/elive/LiveRooms/startLive',
            method: 'post',
            data
        })
    },
    getPushUrl(data) {
        return request({
            url: '/elive/LiveRooms/getPushUrl',
            method: 'post',
            data
        })
    },
}
